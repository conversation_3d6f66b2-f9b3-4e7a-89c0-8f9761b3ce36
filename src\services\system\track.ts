import { get } from '../api';

// 学员练习计划跟踪响应类型（根据 openapi.json 定义）
export interface TrackExercise {
  e_id: number; // 练习ID
  e_title: string; // 练习标题
  e_type: number; // 练习类型（1：作业单；2：角色扮演）
  e_pic?: string | null; // 练习图片URL
  e_intro?: string | null; // 练习简介
  e_duration?: number | null; // 练习时长（分钟）
  t_id?: number | null; // 老师ID
  t_name?: string | null; // 老师姓名
  t_avatar?: string | null; // 老师头像URL
  t_intro?: string | null; // 老师简介
  depend: number; // 练习依赖（0：不依赖；1：依赖）
  w_id?: number | null; // 作业单ID
  s_id?: number | null; // 场景ID
  el_id?: number | null; // 练习情况ID
  el_status?: number | null; // 练习状态（0：待练习；1：练习中；2：已提交）
  el_btime?: string | null; // 开始练习时间
  el_stime?: string | null; // 提交练习时间
  el_utime?: string | null; // 上次更新时间
}

export interface TrackExerciseListResponse {
  class_name: string; // 班级名称
  exercises: TrackExercise[]; // 练习列表
}

// 获取学员练习计划跟踪数据
export const getTrackExercises = (
  clazzId: number,
  studentId: number,
  tenantId: number
): Promise<TrackExerciseListResponse> => {
  return get(`/sys/track/${clazzId}/${studentId}/exercises`, { tenant_id: tenantId });
};
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, theme } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { 
  UserOutlined, 
  LogoutOutlined, 
  SettingOutlined, 
  TeamOutlined,
  RobotOutlined,
  ApartmentOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  CrownOutlined,
  ToolOutlined,
  CloudServerOutlined,
  ProductOutlined,
  IdcardOutlined,
  BookOutlined,
  BulbOutlined,
  CodepenOutlined,
  QuestionCircleOutlined,
  ContactsOutlined,
  VideoCameraOutlined,
  CalendarOutlined,
  UserSwitchOutlined,
  FieldTimeOutlined
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useAuthStore } from '../utils/authStore';
import { logout } from '../services/system/auth';
import GlobalTenantSelector from '../components/GlobalTenantSelector';

const { Header, Content, Sider } = Layout;

const SystemLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const { token: { colorBgContainer, borderRadiusLG } } = theme.useToken();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout: authLogout } = useAuthStore();

      // 判断是否显示租户选择器
    const shouldShowTenantSelector = ['/system/student', '/system/subject', '/system/framework', '/system/module', '/system/character', '/system/question', '/system/worksheet', '/system/scene', '/system/plan', '/system/teacher', '/system/clazz', '/system/track'].includes(location.pathname) || location.pathname.startsWith('/system/question/') || location.pathname.startsWith('/system/worksheet/') || location.pathname.startsWith('/system/scene/') || location.pathname.startsWith('/system/plan/') || location.pathname.startsWith('/system/clazz/') || location.pathname.startsWith('/system/track/') || location.pathname.startsWith('/system/track/exercises/');

  // 监听路由变化，更新菜单展开状态
  useEffect(() => {
    // 如果是个人信息页面，不更新菜单展开状态，保持当前状态
    if (location.pathname === '/system/profile') {
      return;
    }
    const newOpenKeys = getOpenKeys();
    setOpenKeys(newOpenKeys);
  }, [location.pathname]);

  // 监听菜单折叠状态变化，当展开时恢复正确的菜单展开状态
  useEffect(() => {
    if (!collapsed) {
      // 菜单展开时，重新设置正确的展开状态
      if (location.pathname !== '/system/profile') {
        const newOpenKeys = getOpenKeys();
        setOpenKeys(newOpenKeys);
      }
    }
  }, [collapsed, location.pathname]);

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      authLogout();
      navigate('/system/login');
    } catch {
      // 即使登出接口失败，也清除本地状态
      authLogout();
      navigate('/system/login');
    }
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === '/system/home') return ['home'];
    if (path === '/system/admin') return ['admin'];
    if (path === '/system/user') return ['user'];
    if (path === '/system/bot') return ['bot'];
    if (path === '/system/bot-config') return ['bot-config'];
    if (path === '/system/tenant') return ['tenant'];
    if (path === '/system/tenant-bot-config') return ['tenant-bot-config'];
    if (path === '/system/tenant-admin') return ['tenant-admin'];
    if (path === '/system/student') return ['student'];
    if (path === '/system/subject') return ['subject'];
    if (path === '/system/framework' || path === '/system/module') return ['framework'];
    if (path === '/system/character') return ['character'];
    if (path === '/system/question' || path.startsWith('/system/question/')) return ['question'];
    if (path === '/system/worksheet' || path.startsWith('/system/worksheet/')) return ['worksheet'];
    if (path === '/system/scene' || path.startsWith('/system/scene/')) return ['scene'];
    if (path === '/system/plan' || path.startsWith('/system/plan/')) return ['plan'];
    if (path === '/system/teacher') return ['teacher'];
    if (path === '/system/clazz' || path.startsWith('/system/clazz/')) return ['clazz'];
    if (path === '/system/track' || path.startsWith('/system/track/')) return ['track'];
    if (path === '/system/profile') return []; // 个人信息页面清除菜单选中状态
    if (path.startsWith('/tenant')) return ['tenant-space'];
    return ['home'];
  };

  // 获取当前展开的子菜单
  const getOpenKeys = () => {
    const path = location.pathname;
    
    // 根据路径确定展开的菜单
    // 所有系统功能在"平台管理"下
    if (path.startsWith('/system/') && path !== '/system/home' && path !== '/system/student' && path !== '/system/subject' && path !== '/system/framework' && path !== '/system/module' && path !== '/system/character' && path !== '/system/question' && path !== '/system/worksheet' && path !== '/system/scene' && path !== '/system/plan' && path !== '/system/teacher' && path !== '/system/clazz' && path !== '/system/track' && !path.startsWith('/system/question/') && !path.startsWith('/system/worksheet/') && !path.startsWith('/system/scene/') && !path.startsWith('/system/plan/') && !path.startsWith('/system/clazz/') && !path.startsWith('/system/track/') && !path.startsWith('/system/track/exercises/')) {
      return ['system-space'];
    }
    // 租户空间功能 - 包括问题详情页面、作业单详情页面、场景详情页面、计划详情页面、班级详情页面和教学跟踪页面
    if (
      path === '/system/student' ||
      path === '/system/subject' ||
      path === '/system/framework' ||
      path === '/system/module' ||
      path === '/system/character' ||
      path === '/system/question' ||
      path === '/system/worksheet' ||
      path === '/system/scene' ||
      path === '/system/plan' ||
      path === '/system/teacher' ||
      path === '/system/clazz' ||
      path === '/system/track' ||
      path === '/system/track/' ||
      path.startsWith('/system/question/') ||
      path.startsWith('/system/worksheet/') ||
      path.startsWith('/system/scene/') ||
      path.startsWith('/system/plan/') ||
      path.startsWith('/system/clazz/') ||
      path.startsWith('/system/track/')
    ) {
      return ['tenant-space'];
    }
    return [];
  };

  // 处理菜单展开/折叠变化
  const handleMenuOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // 用户菜单点击处理
  const handleUserMenuClick = ({ key }: { key: string }) => {
    if (key === 'profile') {
      navigate('/system/profile');
    } else if (key === 'logout') {
      handleLogout();
    }
  };

  // 侧边栏菜单点击处理
  const handleSideMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'home':
        navigate('/system/home');
        break;
      case 'admin':
        navigate('/system/admin');
        break;
      case 'user':
        navigate('/system/user');
        break;
      case 'bot':
        navigate('/system/bot');
        break;
      case 'bot-config':
        navigate('/system/bot-config');
        break;
      case 'tenant':
        navigate('/system/tenant');
        break;
      case 'tenant-bot-config':
        navigate('/system/tenant-bot-config');
        break;
      case 'tenant-admin':
        navigate('/system/tenant-admin');
        break;
      case 'student':
        navigate('/system/student');
        break;
      case 'subject':
        navigate('/system/subject');
        break;
      case 'framework':
        navigate('/system/framework');
        break;
      case 'character':
        navigate('/system/character');
        break;
      case 'question':
        navigate('/system/question');
        break;
      case 'worksheet':
        navigate('/system/worksheet');
        break;
      case 'scene':
        navigate('/system/scene');
        break;
      case 'plan':
        navigate('/system/plan');
        break;
      case 'teacher':
        navigate('/system/teacher');
        break;
      case 'clazz':
        navigate('/system/clazz');
        break;
      case 'track':
        navigate('/system/track');
        break;
      case 'profile':
        navigate('/system/profile');
        break;
      default:
        break;
    }
  };

  // 根据用户角色生成侧边栏菜单
  const getSideMenuItems = (): MenuProps['items'] => {
    const isSystemAdmin = user?.role === 0; // 0-超级管理员, 1-普通管理员

    // 构建平台管理子菜单
    const systemSpaceChildren: MenuProps['items'] = [];

    if (isSystemAdmin) {
      // 超级管理员可以看到用户管理和管理员管理
      systemSpaceChildren.push(
        {
          key: 'user',
          icon: <UserOutlined />,
          label: '用户管理',
        },
        {
          key: 'admin',
          icon: <CrownOutlined />,
          label: '管理员管理',
        },
        {
          key: 'bot',
          icon: <RobotOutlined />,
          label: '机器人管理',
        },
        {
          key: 'bot-config',
          icon: <SettingOutlined />,
          label: '全局机器人设置',
        },
        {
          key: 'tenant-bot-config',
          icon: <ToolOutlined />,
          label: '租户机器人设置',
        }
      );
    }

    // 租户管理 - 超级管理员和普通管理员都能看到
    systemSpaceChildren.push({
      key: 'tenant',
      icon: <ApartmentOutlined />,
      label: '租户管理',
    });

    // 租户管理员 - 超级管理员和普通管理员都能看到
    systemSpaceChildren.push({
      key: 'tenant-admin',
      icon: <TeamOutlined />,
      label: '租户管理员',
    });

    // 构建租户空间子菜单
    const tenantSpaceChildren: MenuProps['items'] = [
      {
        key: 'student',
        icon: <IdcardOutlined />,
        label: '学员管理',
      },
      {
        key: 'subject',
        icon: <BulbOutlined />,
        label: '主题管理',
      },
      {
        key: 'framework',
        icon: <CodepenOutlined />,
        label: '理论框架管理',
      },
      {
        key: 'question',
        icon: <QuestionCircleOutlined />,
        label: '问题库',
      },
      {
        key: 'character',
        icon: <ContactsOutlined />,
        label: '人物角色库',
      },
      {
        key: 'worksheet',
        icon: <BookOutlined />,
        label: '作业单管理',
      },
      {
        key: 'scene',
        icon: <VideoCameraOutlined />,
        label: '场景管理',
      },
      {
        key: 'plan',
        icon: <CalendarOutlined />,
        label: '练习计划',
      },
      {
        key: 'teacher',
        icon: <UserSwitchOutlined />,
        label: '老师管理',
      },
      {
        key: 'clazz',
        icon: <TeamOutlined />,
        label: '班级管理',
      },
      {
        key: 'track',
        icon: <FieldTimeOutlined />,
        label: '教学跟踪',
      }
    ];

    // 平台管理主菜单
    const menuItems: MenuProps['items'] = [
      {
        key: 'home',
        icon: <HomeOutlined />,
        label: '主页',
      },
      {
        key: 'system-space',
        icon: <CloudServerOutlined />,
        label: '平台管理',
        children: systemSpaceChildren,
      },
      {
        key: 'tenant-space',
        icon: <ProductOutlined />,
        label: '租户空间',
        children: tenantSpaceChildren,
      }
    ];

    return menuItems;
  };

  return (
    <Layout style={{ minHeight: '100vh', display: 'flex' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          transition: 'width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s',
          overflow: 'auto',
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 200,
        }}
      >
        <div 
          className="logo" 
          style={{
            height: 32,
            margin: 16,
            color: 'white',
            fontSize: collapsed ? '14px' : '18px',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            overflow: 'hidden',
            whiteSpace: 'nowrap',
            transition: 'all 0.3s cubic-bezier(0.2, 0, 0, 1) 0s'
          }}
        >
          {collapsed ? 'TMAI' : 'TMAI系统管理后台'}
        </div>
        <div
          style={{
            height: 'calc(100vh - 64px)',
            overflowY: 'auto',
            overflowX: 'hidden',
            // Firefox 滚动条样式
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(255, 255, 255, 0.2) transparent',
          }}
          className="custom-scrollbar"
        >
          <Menu
            theme="dark"
            mode="inline"
            selectedKeys={getSelectedKeys()}
            openKeys={openKeys}
            onOpenChange={handleMenuOpenChange}
            onClick={handleSideMenuClick}
            items={getSideMenuItems()}
            style={{
              transition: 'width 0.3s, opacity 0.3s cubic-bezier(0.2, 0, 0, 1) 0s',
              fontSize: 14, // 使用较大的字体
              border: 'none', // 移除边框
              background: 'transparent', // 透明背景
            }}
          />
        </div>
      </Sider>
      
      <Layout style={{ 
        marginLeft: collapsed ? 80 : 200,
        transition: 'margin-left 0.3s cubic-bezier(0.2, 0, 0, 1) 0s',
        minHeight: '100vh',
      }}>
        <Header style={{ 
          padding: 0, 
          background: colorBgContainer, 
          position: 'sticky',
          top: 0,
          zIndex: 100,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            paddingRight: 16 
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ fontSize: '16px', width: 64, height: 64 }}
              />
              
              {/* 全局租户选择器 - 仅在学员管理页面显示 */}
              {shouldShowTenantSelector && (
                <div>
                  <GlobalTenantSelector 
                    placeholder="当前租户"
                    width={200}
                    disabled={
                      location.pathname === '/system/module' ||
                      location.pathname.startsWith('/system/question/') ||
                      location.pathname.startsWith('/system/worksheet/') ||
                      location.pathname.startsWith('/system/scene/') ||
                      location.pathname.startsWith('/system/plan/') ||
                      location.pathname.startsWith('/system/clazz/') ||
                      location.pathname.startsWith('/system/track/exercises/')
                    }
                  />
                </div>
              )}
            </div>
            
            <Dropdown
              placement="bottomRight"
              popupRender={() => (
                <Menu
                  onClick={handleUserMenuClick}
                  items={[
                    {
                      key: 'profile',
                      icon: <UserOutlined />,
                      label: '个人信息'
                    },
                    {
                      type: 'divider'
                    },
                    {
                      key: 'logout',
                      icon: <LogoutOutlined />,
                      label: '退出登录'
                    }
                  ]}
                />
              )}
            >
              <div style={{ 
                cursor: 'pointer', 
                display: 'flex', 
                alignItems: 'center' 
              }}>
                <span style={{ marginRight: 8 }}>{user?.name}</span>
                <Avatar icon={<UserOutlined />} />
              </div>
            </Dropdown>
          </div>
        </Header>
        
        <Content
          style={{
            margin: '18px 16px',
            padding: 24,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default SystemLayout; 
import React, { useEffect, useState, useCallback } from 'react';
import { Card, Breadcrumb, Spin, Typography, Empty, Layout, Menu, Avatar } from 'antd';
import { HomeOutlined, UserOutlined, InfoCircleFilled, InfoCircleOutlined } from '@ant-design/icons';
import { Link, useParams } from 'react-router-dom';
import {
  getTrackWorksheet,
  getTrackWorksheetUnits,
  getTrackUnitQuestions,
  type TrackWorksheetBasicResponse,
  type TrackWorksheetUnitsResponse,
  type TrackUnitResponse,
  type TrackQuestionResponse
} from '../../../services/system/track';
import { getClazz, getClazzStudents, type ClazzResponse, type ClassStudentWithStudentInfoResponse } from '../../../services/system/clazz';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';

const { Title, Text } = Typography;
const { Sider, Content } = Layout;

// 简单的 Markdown 渲染函数
const renderMarkdown = (content: string) => {
  if (!content) return '';

  // 简单的 Markdown 转换
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
    .replace(/`(.*?)`/g, '<code>$1</code>') // 行内代码
    .replace(/\n/g, '<br>'); // 换行
};

const TrackWorksheet: React.FC = () => {
  const { classId, studentId, worksheetId } = useParams();
  const classIdNum = classId ? Number(classId) : undefined;
  const studentIdNum = studentId ? Number(studentId) : undefined;
  const worksheetIdNum = worksheetId ? Number(worksheetId) : undefined;

  const [worksheetBasic, setWorksheetBasic] = useState<TrackWorksheetBasicResponse | null>(null);
  const [worksheetUnits, setWorksheetUnits] = useState<TrackWorksheetUnitsResponse | null>(null);
  const [selectedUnit, setSelectedUnit] = useState<TrackUnitResponse | null>(null);
  const [unitQuestions, setUnitQuestions] = useState<TrackQuestionResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  const [showBackground, setShowBackground] = useState(true);
  const [clazzInfo, setClazzInfo] = useState<ClazzResponse | null>(null);
  const [studentInfo, setStudentInfo] = useState<ClassStudentWithStudentInfoResponse | null>(null);

  // 获取班级信息
  const fetchClazzInfo = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum) return;
      const res = await getClazz(classIdNum, tenantInfo.id);
      setClazzInfo(res);
    } catch (e) {
      showError(e, '获取班级信息失败');
    }
  }, [classIdNum]);

  // 获取学员信息
  const fetchStudentInfo = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum || !studentIdNum) return;

      const res = await getClazzStudents(classIdNum, tenantInfo.id, { active: 1, limit: 100 });
      const student = res.items.find(s => s.sid === studentIdNum);
      setStudentInfo(student || null);
    } catch (e) {
      showError(e, '获取学员信息失败');
    }
  }, [classIdNum, studentIdNum]);

  // 获取作业单基本信息
  const fetchWorksheetBasic = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum || !studentIdNum || !worksheetIdNum) return;

      const res = await getTrackWorksheet(worksheetIdNum, classIdNum, studentIdNum, tenantInfo.id);
      setWorksheetBasic(res);
    } catch (e) {
      showError(e, '获取作业单信息失败');
    }
  }, [classIdNum, studentIdNum, worksheetIdNum]);

  // 获取作业单单元列表
  const fetchWorksheetUnits = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !worksheetIdNum) return;

      const res = await getTrackWorksheetUnits(worksheetIdNum, tenantInfo.id);
      setWorksheetUnits(res);
    } catch (e) {
      showError(e, '获取单元列表失败');
    }
  }, [worksheetIdNum]);

  // 获取单元问题列表
  const fetchUnitQuestions = useCallback(async (unitId: number) => {
    try {
      setQuestionsLoading(true);
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !worksheetIdNum || !worksheetBasic?.elid) return;

      const res = await getTrackUnitQuestions(worksheetIdNum, unitId, worksheetBasic.elid, tenantInfo.id);
      setUnitQuestions(res.question_list);
    } catch (e) {
      showError(e, '获取问题列表失败');
      setUnitQuestions([]);
    } finally {
      setQuestionsLoading(false);
    }
  }, [worksheetIdNum, worksheetBasic?.elid]);

  // 获取所有数据
  const fetchAllData = useCallback(async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchClazzInfo(),
        fetchStudentInfo(),
        fetchWorksheetBasic(),
        fetchWorksheetUnits()
      ]);
    } catch (e) {
      showError(e, '获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [fetchClazzInfo, fetchStudentInfo, fetchWorksheetBasic, fetchWorksheetUnits]);

  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // 处理单元选择
  const handleUnitSelect = (unit: TrackUnitResponse) => {
    setSelectedUnit(unit);
    setShowBackground(false);
    fetchUnitQuestions(unit.id);
  };

  // 处理显示背景信息
  const handleShowBackground = () => {
    setSelectedUnit(null);
    setShowBackground(true);
    setUnitQuestions([]);
  };

  // 获取状态标签
  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>待练习</span>;
      case 1:
        return <span style={{ color: '#21808d', backgroundColor: '#e6fffb', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>练习中</span>;
      case 2:
        return <span style={{ color: '#13343b', backgroundColor: '#f6ffed', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>已提交</span>;
      default:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>未开始</span>;
    }
  };

  // 渲染老师信息
  const renderTeacherInfo = () => {
    if (!worksheetBasic?.tname) return null;

    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {worksheetBasic.tavatar ? (
          <Avatar
            src={worksheetBasic.tavatar}
            size={20}
            style={{ marginRight: '4px' }}
          />
        ) : (
          <UserOutlined style={{ marginRight: '4px' }} />
        )}
        <Text type="secondary">指导老师: {worksheetBasic.tname}</Text>
      </div>
    );
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 150px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb
        style={{ marginBottom: 12, flexShrink: 0 }}
        items={[
          {
            title: <Link to="/system/home"><HomeOutlined /> 主页</Link>,
          },
          {
            title: '租户空间',
          },
          {
            title: <Link to="/system/track">教学跟踪</Link>,
          },
          {
            title: <Link to={`/system/track/exercises/${classId}?studentId=${studentId}`}>{clazzInfo?.name || '班级练习跟踪'}</Link>,
          },
          {
            title: worksheetBasic?.title || '作业单详情',
          },
        ]}
      />

      {loading ? (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: '80vh'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text type="secondary">正在加载作业单信息...</Text>
          </div>
        </div>
      ) : worksheetBasic && worksheetUnits ? (
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* 顶部基本信息部分 */}
          <div style={{ marginBottom: '16px' }}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '12px' }}>
                <Title level={2} style={{ margin: 0 }}>
                  {worksheetBasic.title}
                </Title>
                {getStatusTag(worksheetBasic.status)}
              </div>

              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
                  {renderTeacherInfo()}

                  {worksheetBasic.duration && (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Text type="secondary">预计时长: {worksheetBasic.duration} 分钟</Text>
                    </div>
                  )}
                </div>

                {/* 学员信息 */}
                {studentInfo && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      icon={<UserOutlined />}
                      size={20}
                      style={{ marginRight: '8px', backgroundColor: '#1677ff' }}
                    />
                    <Text strong>{studentInfo.name}（ID: {studentInfo.sid}）</Text>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* 下半部分 - 左右布局 */}
          <Layout style={{
            flex: 1,
            boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)',
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            {/* 左侧单元列表 */}
            <Sider
              width={240}
              style={{
                background: '#fff',
                borderRight: '1px solid #f0f0f0'
              }}
            >
              <div style={{ padding: '16px' }}>
                <div style={{ marginBottom: '16px' }}>
                  <div
                    onClick={handleShowBackground}
                    style={{
                      padding: '8px 12px',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      backgroundColor: showBackground ? '#e6f4ff' : 'transparent',
                      color: showBackground ? '#1677ff' : '#666',
                      display: 'flex',
                      alignItems: 'center',
                      transition: 'all 0.2s'
                    }}
                  >
                    {showBackground ?
                      <InfoCircleFilled style={{ marginRight: '8px', fontSize: '16px' }} /> :
                      <InfoCircleOutlined style={{ marginRight: '8px', fontSize: '16px' }} />
                    }
                    练习背景
                  </div>
                </div>

                <Menu
                  mode="inline"
                  selectedKeys={selectedUnit ? [selectedUnit.id.toString()] : []}
                  style={{ border: 'none' }}
                  items={worksheetUnits.unit_list.map((unit, index) => ({
                    key: unit.id.toString(),
                    label: (
                      <div>
                        <span style={{ marginRight: '8px', color: '#999' }}>{index + 1}.</span>
                        {unit.name}
                      </div>
                    ),
                    onClick: () => handleUnitSelect(unit),
                  }))}
                />
              </div>
            </Sider>

            {/* 右侧内容区域 */}
            <Layout>
              <Content style={{
                padding: '24px',
                background: '#fff',
                overflow: 'auto'
              }}>
                {showBackground && (
                  <div>
                    <div style={{ marginBottom: '18px' }}>
                      <Title level={3} style={{ display: 'flex', alignItems: 'center' }}>
                        <InfoCircleFilled style={{ marginRight: '8px', color: '#1677ff' }} />
                        练习背景
                      </Title>
                    </div>
                    <div style={{
                      padding: '16px',
                      backgroundColor: '#fafafa',
                      borderRadius: '8px',
                      border: '1px solid #f0f0f0'
                    }}>
                      <div dangerouslySetInnerHTML={{ __html: worksheetBasic.bgtext || '暂无背景信息' }} />
                    </div>
                  </div>
                )}

                {selectedUnit && (
                  <>
                    <div style={{ marginBottom: '18px' }}>
                      <Title level={3}>
                        <span style={{
                          marginRight: '8px',
                          color: '#999',
                          fontSize: '18px'
                        }}>
                          {(worksheetUnits?.unit_list?.findIndex(unit => unit.id === selectedUnit?.id) ?? -1) + 1}.
                        </span>
                        {selectedUnit.name}
                      </Title>
                      {selectedUnit.bgtext && (
                        <div style={{
                          padding: '16px',
                          backgroundColor: '#fafafa',
                          borderRadius: '8px',
                          border: '1px solid #f0f0f0',
                          marginTop: '12px'
                        }}>
                          <div dangerouslySetInnerHTML={{ __html: selectedUnit.bgtext }} />
                        </div>
                      )}
                    </div>

                    <Spin spinning={questionsLoading}>
                      {unitQuestions.length > 0 ? (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                          {unitQuestions.map((question, index) => (
                            <Card key={question.id} style={{ marginBottom: '16px' }}>
                              <Title level={4}>
                                <span style={{ marginRight: '8px', color: '#999' }}>
                                  {(worksheetUnits?.unit_list?.findIndex(unit => unit.id === selectedUnit?.id) ?? -1) + 1}.{index + 1}
                                </span>
                                {question.title}
                              </Title>

                              {question.bgtext && (
                                <div style={{
                                  padding: '12px',
                                  backgroundColor: '#f9f9f9',
                                  borderRadius: '6px',
                                  marginBottom: '12px'
                                }}>
                                  <div dangerouslySetInnerHTML={{ __html: question.bgtext }} />
                                </div>
                              )}

                              {question.draft && (
                                <div>
                                  <Text strong>学员作答草稿：</Text>
                                  <div style={{
                                    padding: '12px',
                                    backgroundColor: '#f0f9ff',
                                    borderRadius: '6px',
                                    marginTop: '8px',
                                    whiteSpace: 'pre-wrap'
                                  }}>
                                    {question.draft}
                                  </div>
                                </div>
                              )}

                              <div style={{ marginTop: '12px' }}>
                                <Text strong>学员最终作答：</Text>
                                <div style={{
                                  padding: '12px',
                                  backgroundColor: '#f6ffed',
                                  borderRadius: '6px',
                                  marginTop: '8px'
                                }}>
                                  {question.answer && question.answer.trim() ? (
                                    <div dangerouslySetInnerHTML={{ __html: renderMarkdown(question.answer) }} />
                                  ) : (
                                    <Text type="secondary">暂无作答</Text>
                                  )}
                                </div>
                              </div>

                              <div style={{ marginTop: '12px' }}>
                                <Text strong>AI老师点评：</Text>
                                <div style={{
                                  padding: '12px',
                                  backgroundColor: '#fff7e6',
                                  borderRadius: '6px',
                                  marginTop: '8px'
                                }}>
                                  {question.comment && question.comment.trim() ? (
                                    <div dangerouslySetInnerHTML={{ __html: renderMarkdown(question.comment) }} />
                                  ) : (
                                    <Text type="secondary">暂无点评</Text>
                                  )}
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <Empty description="暂无问题" />
                      )}
                    </Spin>
                  </>
                )}

                {!selectedUnit && !showBackground && (
                  <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px'
                  }}>
                    <Empty description="请从左侧选择一个单元或查看练习背景" />
                  </div>
                )}
              </Content>
            </Layout>
          </Layout>
        </div>
      ) : (
        <Empty
          description="暂无作业单信息"
          style={{ padding: '50px' }}
        />
      )}
    </div>
  );
};

export default TrackWorksheet;
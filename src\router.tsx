import { createBrowserRouter, Navigate } from 'react-router-dom';
import SystemLayout from './layouts/SystemLayout';
import SystemLogin from './pages/system/login';
import SystemHome from './pages/system/home';
import SystemAdminManagement from './pages/system/admin';
import SystemProfile from './pages/system/profile';
import SystemUserManagement from './pages/system/user';
import BotManagement from './pages/system/bot';
import BotConfigManagement from './pages/system/bot-config';
import TenantManagement from './pages/system/tenant';
import TenantBotConfigManagement from './pages/system/tenant-bot-config';
import TenantAdminManagement from './pages/system/tenant-admin';
import StudentManagement from './pages/system/student';
import SubjectManagement from './pages/system/subject';
import FrameworkManagement from './pages/system/framework';
import ModuleManagement from './pages/system/module';
import QuestionManagement from './pages/system/question';
import QuestionDetail from './pages/system/question/QuestionDetail';
import WorksheetManagement from './pages/system/worksheet';
import WorksheetDetail from './pages/system/worksheet/WorksheetDetail';
import CharacterManagement from './pages/system/character';
import SceneManagement from './pages/system/scene';
import SceneDetail from './pages/system/scene/SceneDetail';
import PlanManagement from './pages/system/plan';
import PlanDetail from './pages/system/plan/PlanDetail';
import TeacherManagement from './pages/system/teacher';
import RequireSystemAuth from './components/auth/RequireSystemAuth';
import RequireSystemAdmin from './components/auth/RequireSystemAdmin';
import ClazzManagement from './pages/system/clazz';
import ClazzDetail from './pages/system/clazz/ClazzDetail';
import TrackManagement from './pages/system/track';
import TrackExercises from './pages/system/track/trackExercises';
import TrackWorksheet from './pages/system/track/trackWorksheet';

// 创建路由
const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/system/login" replace />,
  },
  {
    path: '/system/login',
    element: <SystemLogin />,
  },
  {
    path: '/system',
    element: (
      <RequireSystemAuth>
        <SystemLayout />
      </RequireSystemAuth>
    ),
    children: [
      {
        path: '',
        element: <Navigate to="/system/home" replace />,
      },
      {
        path: 'home',
        element: <SystemHome />,
      },
      {
        path: 'user',
        element: (
          <RequireSystemAdmin>
            <SystemUserManagement />
          </RequireSystemAdmin>
        ),
      },
      {
        path: 'admin',
        element: (
          <RequireSystemAdmin>
            <SystemAdminManagement />
          </RequireSystemAdmin>
        ),
      },
      {
        path: 'bot',
        element: (
          <RequireSystemAdmin>
            <BotManagement />
          </RequireSystemAdmin>
        ),
      },
      {
        path: 'bot-config',
        element: (
          <RequireSystemAdmin>
            <BotConfigManagement />
          </RequireSystemAdmin>
        ),
      },
      {
        path: 'tenant',
        element: <TenantManagement />,
      },
      {
        path: 'tenant-bot-config',
        element: (
          <RequireSystemAdmin>
            <TenantBotConfigManagement />
          </RequireSystemAdmin>
        ),
      },
      {
        path: 'tenant-admin',
        element: <TenantAdminManagement />,
      },
      {
        path: 'student',
        element: <StudentManagement />,
      },
      {
        path: 'subject',
        element: <SubjectManagement />,
      },
      {
        path: 'framework',
        element: <FrameworkManagement />,
      },
      {
        path: 'module',
        element: <ModuleManagement />,
      },
      {
        path: 'character',
        element: <CharacterManagement />,
      },
      {
        path: 'question',
        element: <QuestionManagement />,
      },
      {
        path: 'question/:id',
        element: <QuestionDetail />,
      },
      {
        path: 'worksheet',
        element: <WorksheetManagement />,
      },
      {
        path: 'worksheet/:id',
        element: <WorksheetDetail />,
      },
      {
        path: 'scene',
        element: <SceneManagement />,
      },
      {
        path: 'scene/:id',
        element: <SceneDetail />,
      },
      {
        path: 'plan',
        element: <PlanManagement />,
      },
      {
        path: 'plan/:id',
        element: <PlanDetail />,
      },
      {
        path: 'teacher',
        element: <TeacherManagement />,
      },
      {
        path: 'clazz',
        element: <ClazzManagement />,
      },
      {
        path: 'clazz/:id',
        element: <ClazzDetail />,
      },
      {
        path: 'track',
        element: <TrackManagement />,
      },
      {
        path: 'track/exercises/:classId',
        element: <TrackExercises />,
      },
      {
        path: 'track/worksheet/:classId/:studentId/:worksheetId',
        element: <TrackWorksheet />,
      },
      {
        path: 'profile',
        element: <SystemProfile />,
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/system/login" replace />,
  },
]);

export default router; 